package http

import (
	"database/sql"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/code"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/config"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/labstack/echo/v4"
)

// WSPkgHttpRoutes handles HTTP routes that integrate with the new wspkg WebSocket package
type WSPkgHttpRoutes struct {
	manager     *wspkg.Manager
	ClientState *wspkg.ClientState
	Pubsub      *wspkg.PubSubManager
	query       *database.Queries
	cfg         *config.Config
}

// NewWSPkgHttpRoutes creates a new instance of WSPkgHttpRoutes
func NewWSPkgHttpRoutes(manager *wspkg.Manager, clientState *wspkg.ClientState, pubsub *wspkg.PubSubManager, query *database.Queries, cfg *config.Config) *WSPkgHttpRoutes {
	return &WSPkgHttpRoutes{
		manager:     manager,
		ClientState: clientState,
		Pubsub:      pubsub,
		query:       query,
		cfg:         cfg,
	}
}

// OpenDoorWSPkg is the new simplified open door endpoint using wspkg
func (h *WSPkgHttpRoutes) OpenDoorWSPkg(c echo.Context) error {
	var req types.DoorLockAccessReq
	err := c.Bind(&req)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Request binding failed: %v", err)
		return c.JSON(http.StatusBadRequest, "request is not valid")
	}

	log.Printf("[OpenDoorWSPkg] Processing request for user ID: %d, QR code length: %d", req.User.ID, len(req.QRcode))

	// Validate JWT token and extract QR code info
	claim, err := utils.ValidateToken(req.QRcode, []byte(h.cfg.JWTSecret))
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Invalid QR token: %v", err)
		return c.JSON(http.StatusUnauthorized, "invalid qr code")
	}
	qrData := claim.QRInfo

	log.Printf("[OpenDoorWSPkg] QR Data extracted - ReqDeviceId: %s, ClientId: %s, ConnType: %s, BranchId: %d",
		qrData.ReqDeviceId, qrData.ClientId, qrData.ConnType, qrData.BranchId)

	// Validate that the target door device is connected and get door client
	doorClient, err := h.ClientState.GetDoorClient(qrData.ReqDeviceId)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Door client not found: %v", err)
		return c.JSON(http.StatusBadRequest, "target device not connected")
	}

	// Check if door is processing
	if doorClient.CheckIsProcessing() {
		log.Printf("[ERROR - OpenDoorWSPkg] Door client %s is already processing", qrData.ReqDeviceId)
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINUSE, "door is already in use"))
	}

	// Validate QR client exists and is connected
	qrClient, err := h.ClientState.GetQRClient(qrData.ClientId)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] QR client not found: %v", err)
		return c.JSON(http.StatusBadRequest, "QR client not connected")
	}

	// Validate QR type matches
	if qrClient.CheckType() != qrData.ConnType {
		log.Printf("[ERROR - OpenDoorWSPkg] QR type mismatch: expected %s, got %s", qrClient.CheckType(), qrData.ConnType)
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINVALIED, "QR type mismatch"))
	}

	log.Printf("[OpenDoorWSPkg] QR client %s validated successfully", qrData.ClientId)

	// Set processing status for QR clients using dedicated endpoint
	err = h.sendQRProcessingStatus(qrData.ClientId, true)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Failed to send processing status: %v", err)
		return c.JSON(http.StatusInternalServerError, "failed to send processing status")
	}

	// Set door processing state
	doorClient.SetIsProcessing(true)

	// Check if user exists, create if not
	var userID int64
	userID, err = h.query.GetAttendeeIDByUserID(c.Request().Context(), int64(req.User.ID))
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// Create new user
			err := h.query.InsertDoorLockUser(c.Request().Context(), database.InsertDoorLockUserParams{
				UserID:    int64(req.User.ID),
				Username:  req.User.Name,
				BranchID:  int64(qrData.BranchId),
				Email:     sql.NullString{String: req.User.Email, Valid: true},
				Role:      sql.NullString{String: req.User.Role, Valid: true},
				Phone:     sql.NullString{String: req.User.Phone, Valid: true},
				Nic:       sql.NullString{String: req.User.NIC, Valid: true},
				AvatarUrl: sql.NullString{String: req.User.AvatarURL, Valid: true},
			})
			if err != nil {
				log.Printf("[ERROR - OpenDoorWSPkg] Failed to insert user: %v", err)
				h.sendErrorMessages(doorClient, "failed to create user")
				return c.JSON(http.StatusInternalServerError, "failed to insert user")
			}

			userID, err = h.query.GetAttendeeIDByUserID(c.Request().Context(), int64(req.User.ID))
			if err != nil {
				log.Printf("[ERROR - OpenDoorWSPkg] Failed to get user ID after creation: %v", err)
				h.sendErrorMessages(doorClient, "failed to get user id")
				return c.JSON(http.StatusInternalServerError, "failed to get user id")
			}
		} else {
			log.Printf("[ERROR - OpenDoorWSPkg] Database error: %v", err)
			h.sendErrorMessages(doorClient, "user lookup failed")
			return c.JSON(http.StatusBadRequest, "user not found")
		}
	}

	// Check attendance logic
	timeNow := time.Now()
	dateOnly := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0, time.UTC)

	err = h.validateAttendanceLogic(c, req.User, qrData, dateOnly, doorClient)
	if err != nil {
		return err // Error response already sent in validateAttendanceLogic
	}

	// Check if door is already open
	if doorClient.IsOpen() {
		h.sendErrorMessages(doorClient, "door already open")
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.DoorOpen, "door open already"))
	}

	// Trigger door unlock using the event system
	err = h.triggerDoorUnlock(qrData.ReqDeviceId, userID, req.User, qrData)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Failed to trigger door unlock: %v", err)
		h.sendErrorMessages(doorClient, "failed to unlock door")
		return c.JSON(http.StatusInternalServerError, "failed to trigger door unlock")
	}

	// Create attendance record
	err = h.createAttendanceRecord(c, userID, req.User, qrData)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Failed to create attendance record: %v", err)
		// Don't fail the request for attendance record creation failure
	}

	// Send success messages
	err = h.sendSuccessMessages(doorClient)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Failed to send success messages: %v", err)
		// Don't fail the request for success message failure
	}

	// Send generate-new message to requesting QR client
	err = h.sendGenerateNewMessage(qrData.ClientId)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Failed to send generate-new message: %v", err)
		// Don't fail the request for generate-new message failure
	}

	log.Printf("[OpenDoorWSPkg] Door opened successfully for user %d via device %s", req.User.ID, qrData.ReqDeviceId)

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    true,
		"message": "Door opened successfully",
		"user":    req.User.Name,
		"device":  qrData.ReqDeviceId,
	})
}

// sendQRProcessingStatus sends processing status to a specific QR client
func (h *WSPkgHttpRoutes) sendQRProcessingStatus(clientID string, processing bool) error {
	status := "true"
	if !processing {
		status = "false"
	}

	message := wspkg.NewMessage("processing", []byte(fmt.Sprintf(`{"isProcessing": "%s"}`, status)))

	client, exists := h.manager.GetClient(clientID)
	if !exists || client == nil || !client.IsOnline() {
		return fmt.Errorf("QR client %s not available", clientID)
	}

	client.Send(message)
	log.Printf("[WSPkg] Sent processing status (%s) to QR client %s", status, clientID)
	return nil
}

// sendProcessingMessages sends processing status messages to QR clients
func (h *WSPkgHttpRoutes) sendProcessingMessages(doorClient *wspkg.DoorClient, processing bool) error {
	status := "true"
	if !processing {
		status = "false"
	}

	message := wspkg.NewMessage("processing", []byte(fmt.Sprintf(`{"isProcessing": "%s"}`, status)))

	var errors []string

	// Send to QR-in client if it exists
	qrIn := doorClient.GetQRIn()
	if qrIn != nil {
		client, exists := h.manager.GetClient(qrIn.Id)
		if exists && client != nil && client.IsOnline() {
			client.Send(message)
			log.Printf("[WSPkg] Sent processing message to QR-in client %s", qrIn.Id)
		} else {
			errors = append(errors, fmt.Sprintf("QR-in client %s not available", qrIn.Id))
		}
	}

	// Send to QR-out client if it exists
	qrOut := doorClient.GetQROut()
	if qrOut != nil {
		client, exists := h.manager.GetClient(qrOut.Id)
		if exists && client != nil && client.IsOnline() {
			client.Send(message)
			log.Printf("[WSPkg] Sent processing message to QR-out client %s", qrOut.Id)
		} else {
			errors = append(errors, fmt.Sprintf("QR-out client %s not available", qrOut.Id))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to send processing messages: %v", errors)
	}

	return nil
}

// triggerDoorUnlock triggers door unlock using the event system
func (h *WSPkgHttpRoutes) triggerDoorUnlock(doorClientID string, userID int64, user types.TmpUser, qrData utils.QRCodeInfo) error {
	// Convert device ID to int64
	deviceIntId, err := strconv.ParseInt(qrData.ReqDeviceId, 10, 64)
	if err != nil {
		return fmt.Errorf("failed to parse device ID: %w", err)
	}

	// Convert service data
	serviceData, err := types.ServiceDataToNullString(user.ServiceData)
	if err != nil {
		log.Printf("[ERROR] >> %v", err)
	}

	// Convert gym data
	gymData, err := types.GymDataToNullString(&user.GymData)
	if err != nil {
		log.Printf("[ERROR] >> %v", err)
	}

	// Create attendance data for the event
	attendData := database.CreateAttendeeParams{
		AttendeeID: userID,
		State:      qrData.ConnType,
		BranchID:   int64(qrData.BranchId),
		DeviceID: sql.NullInt64{
			Int64: deviceIntId,
			Valid: true,
		},
		Subscription: sql.NullString{
			Valid: false,
		},
		Booktype: sql.NullString{
			String: user.BookType,
			Valid:  true,
		},
		ServiceData: serviceData,
		GymData:     gymData,
	}

	// Create message for the open-door event
	msg, err := wspkg.NewMessageFromJSON("open-door", attendData)
	if err != nil {
		return fmt.Errorf("failed to create open-door message: %w", err)
	}

	// Send the message to the door client to trigger the event
	client, exists := h.manager.GetClient(doorClientID)
	if !exists || client == nil || !client.IsOnline() {
		return fmt.Errorf("door client %s not available", doorClientID)
	}

	client.Send(msg)
	log.Printf("[WSPkg] Triggered door unlock event for client %s", doorClientID)
	return nil
}

// sendSuccessMessages sends success messages to QR clients
func (h *WSPkgHttpRoutes) sendSuccessMessages(doorClient *wspkg.DoorClient) error {
	successMessage := wspkg.NewMessage("unlock-success", []byte(`{"status": "success", "message": "Door opened successfully"}`))

	var errors []string

	// Send to QR-in client if it exists
	qrIn := doorClient.GetQRIn()
	if qrIn != nil {
		client, exists := h.manager.GetClient(qrIn.Id)
		if exists && client != nil && client.IsOnline() {
			client.Send(successMessage)
			log.Printf("[WSPkg] Sent success message to QR-in client %s", qrIn.Id)
		} else {
			errors = append(errors, fmt.Sprintf("QR-in client %s not available", qrIn.Id))
		}
	}

	// Send to QR-out client if it exists
	qrOut := doorClient.GetQROut()
	if qrOut != nil {
		client, exists := h.manager.GetClient(qrOut.Id)
		if exists && client != nil && client.IsOnline() {
			client.Send(successMessage)
			log.Printf("[WSPkg] Sent success message to QR-out client %s", qrOut.Id)
		} else {
			errors = append(errors, fmt.Sprintf("QR-out client %s not available", qrOut.Id))
		}
	}

	if len(errors) > 0 {
		log.Printf("[WSPkg] Some success messages failed to send: %v", errors)
		// Don't return error for success messages as they're not critical
	}

	return nil
}

// sendGenerateNewMessage sends a generate-new message to the requesting QR client
func (h *WSPkgHttpRoutes) sendGenerateNewMessage(clientID string) error {
	client, exists := h.manager.GetClient(clientID)
	if !exists || client == nil || !client.IsOnline() {
		return fmt.Errorf("QR client %s not available", clientID)
	}

	message := wspkg.NewMessage("generate-new", []byte(`{"status": "success", "refresh": true}`))
	client.Send(message)

	log.Printf("[WSPkg] Sent generate-new message to QR client %s", clientID)
	return nil
}

// sendErrorMessages sends error messages to QR clients
func (h *WSPkgHttpRoutes) sendErrorMessages(doorClient *wspkg.DoorClient, errorMsg string) {
	errorMessage := wspkg.NewMessage("processError", []byte(fmt.Sprintf(`{"isProcessing": false, "error": "%s"}`, errorMsg)))

	// Send to QR-in client if it exists
	qrIn := doorClient.GetQRIn()
	if qrIn != nil {
		client, exists := h.manager.GetClient(qrIn.Id)
		if exists && client != nil && client.IsOnline() {
			client.Send(errorMessage)
			log.Printf("[WSPkg] Sent error message to QR-in client %s", qrIn.Id)
		}
	}

	// Send to QR-out client if it exists
	qrOut := doorClient.GetQROut()
	if qrOut != nil {
		client, exists := h.manager.GetClient(qrOut.Id)
		if exists && client != nil && client.IsOnline() {
			client.Send(errorMessage)
			log.Printf("[WSPkg] Sent error message to QR-out client %s", qrOut.Id)
		}
	}
}

// validateAttendanceLogic validates the attendance logic for the user
func (h *WSPkgHttpRoutes) validateAttendanceLogic(ctx echo.Context, user types.TmpUser, qrData utils.QRCodeInfo, dateOnly time.Time, doorClient *wspkg.DoorClient) error {
	if qrData.ConnType == "qr-in" {
		// For entry, check if user has already entered today
		isAlreadyIn, err := h.query.CheckAttendanceRecordExists(ctx.Request().Context(), database.CheckAttendanceRecordExistsParams{
			UserID: int64(user.ID),
			State:  "qr-in",
			Time: sql.NullTime{
				Time:  dateOnly,
				Valid: true,
			},
			Booktype: sql.NullString{
				String: user.BookType,
				Valid:  true,
			},
		})
		if err != nil {
			log.Printf("[ERROR - validateAttendanceLogic] Database error checking entry: %v", err)
			h.sendErrorMessages(doorClient, "attendance check failed")
			return ctx.JSON(http.StatusInternalServerError, "attendance check failed")
		}

		if isAlreadyIn {
			// Check if user has already exited (can re-enter if exited)
			isAlreadyOut, err := h.query.CheckAttendanceRecordExists(ctx.Request().Context(), database.CheckAttendanceRecordExistsParams{
				UserID: int64(user.ID),
				State:  "qr-out",
				Time: sql.NullTime{
					Time:  dateOnly,
					Valid: true,
				},
				Booktype: sql.NullString{
					String: user.BookType,
					Valid:  true,
				},
			})
			if err != nil {
				log.Printf("[ERROR - validateAttendanceLogic] Database error checking exit: %v", err)
				h.sendErrorMessages(doorClient, "attendance check failed")
				return ctx.JSON(http.StatusInternalServerError, "attendance check failed")
			}

			if !isAlreadyOut {
				log.Printf("[ERROR - validateAttendanceLogic] User %d already entered today", user.ID)
				h.sendErrorMessages(doorClient, "user already in")
				return ctx.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.USERALREADYIN, "user already in"))
			}
		}
	} else if qrData.ConnType == "qr-out" {
		// For exit, check if user has entered today
		hasCheckedIn, err := h.query.CheckAttendanceRecordExists(ctx.Request().Context(), database.CheckAttendanceRecordExistsParams{
			UserID: int64(user.ID),
			State:  "qr-in",
			Time: sql.NullTime{
				Time:  dateOnly,
				Valid: true,
			},
			Booktype: sql.NullString{
				String: user.BookType,
				Valid:  true,
			},
		})
		if err != nil {
			log.Printf("[ERROR - validateAttendanceLogic] Database error checking entry: %v", err)
			h.sendErrorMessages(doorClient, "attendance check failed")
			return ctx.JSON(http.StatusInternalServerError, "attendance check failed")
		}

		if !hasCheckedIn {
			log.Printf("[ERROR - validateAttendanceLogic] User %d has not checked in today", user.ID)
			h.sendErrorMessages(doorClient, "user must check in before checking out")
			return ctx.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.USERNOTCHECKEDIN, "user must check in before checking out"))
		}

		// Check if user has already exited
		hasCheckedOut, err := h.query.CheckAttendanceRecordExists(ctx.Request().Context(), database.CheckAttendanceRecordExistsParams{
			UserID: int64(user.ID),
			State:  "qr-out",
			Time: sql.NullTime{
				Time:  dateOnly,
				Valid: true,
			},
			Booktype: sql.NullString{
				String: user.BookType,
				Valid:  true,
			},
		})
		if err != nil {
			log.Printf("[ERROR - validateAttendanceLogic] Database error checking exit: %v", err)
			h.sendErrorMessages(doorClient, "attendance check failed")
			return ctx.JSON(http.StatusInternalServerError, "attendance check failed")
		}

		if hasCheckedOut {
			log.Printf("[ERROR - validateAttendanceLogic] User %d already checked out today", user.ID)
			h.sendErrorMessages(doorClient, "user already checked out")
			return ctx.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.USERALREADYOUT, "user already checked out"))
		}
	}

	return nil
}

// createAttendanceRecord creates an attendance record for the user
func (h *WSPkgHttpRoutes) createAttendanceRecord(ctx echo.Context, userID int64, user types.TmpUser, qrData utils.QRCodeInfo) error {
	// Convert device ID to int64
	deviceIntId, err := strconv.ParseInt(qrData.ReqDeviceId, 10, 64)
	if err != nil {
		return fmt.Errorf("failed to parse device ID: %w", err)
	}

	// Convert service data
	serviceData, err := types.ServiceDataToNullString(user.ServiceData)
	if err != nil {
		log.Printf("[ERROR] >> %v", err)
	}

	// Create attendance record using the correct database method
	err = h.query.CreateAttendee(ctx.Request().Context(), database.CreateAttendeeParams{
		AttendeeID: userID,
		State:      qrData.ConnType,
		BranchID:   int64(qrData.BranchId),
		DeviceID: sql.NullInt64{
			Int64: deviceIntId,
			Valid: true,
		},
		ServiceData: serviceData,
	})
	if err != nil {
		return fmt.Errorf("failed to create attendance record: %w", err)
	}

	log.Printf("[OpenDoorWSPkg] Created attendance record for user %d with state %s", user.ID, qrData.ConnType)
	return nil
}
