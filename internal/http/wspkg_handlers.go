package http

import (
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/code"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/config"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/labstack/echo/v4"
)

// WSPkgHttpRoutes handles HTTP routes that integrate with the new wspkg WebSocket package
type WSPkgHttpRoutes struct {
	manager     *wspkg.Manager
	ClientState *wspkg.ClientState
	Pubsub      *wspkg.PubSubManager
	query       *database.Queries
	cfg         *config.Config
	logger      wspkg.Logger
}

// NewWSPkgHttpRoutes creates a new instance of WSPkgHttpRoutes
func NewWSPkgHttpRoutes(manager *wspkg.Manager, clientState *wspkg.ClientState, pubsub *wspkg.PubSubManager, query *database.Queries, cfg *config.Config) *WSPkgHttpRoutes {
	// Initialize logger
	logger, err := wspkg.NewDefaultLogger()
	if err != nil {
		// Fallback to NoOp logger if default logger fails
		logger = wspkg.NewNoOpLogger()
	}

	return &WSPkgHttpRoutes{
		manager:     manager,
		ClientState: clientState,
		Pubsub:      pubsub,
		query:       query,
		cfg:         cfg,
		logger:      logger,
	}
}

// OpenDoorWSPkg is the new simplified open door endpoint using wspkg
func (h *WSPkgHttpRoutes) OpenDoorWSPkg(c echo.Context) error {
	var req types.DoorLockAccessReq
	err := c.Bind(&req)
	if err != nil {
		h.logger.Error("Request binding failed",
			wspkg.Error(err),
			wspkg.String("endpoint", "OpenDoorWSPkg"))
		return c.JSON(http.StatusBadRequest, "request is not valid")
	}

	h.logger.Info("Processing door open request",
		wspkg.Int("user_id", req.User.ID),
		wspkg.Int("qr_code_length", len(req.QRcode)),
		wspkg.String("endpoint", "OpenDoorWSPkg"))

	// CRITICAL FIX: First extract QR data to get the device ID, then validate with door client's secret key
	// Parse JWT token first to get device information (using global secret for initial parsing)
	claim, err := utils.ValidateToken(req.QRcode, []byte(h.cfg.JWTSecret))
	if err != nil {
		h.logger.Error("Invalid QR token structure",
			wspkg.Error(err),
			wspkg.String("endpoint", "OpenDoorWSPkg"))
		return c.JSON(http.StatusUnauthorized, "invalid qr code")
	}
	qrData := claim.QRInfo

	h.logger.Info("QR Data extracted successfully",
		wspkg.String("req_device_id", qrData.ReqDeviceId),
		wspkg.String("client_id", qrData.ClientId),
		wspkg.String("conn_type", qrData.ConnType),
		wspkg.Int("branch_id", qrData.BranchId),
		wspkg.String("endpoint", "OpenDoorWSPkg"))

	doorId64, err := strconv.ParseInt(qrData.ReqDeviceId, 10, 64)
	if err != nil {
		h.logger.Error("Failed to parse device ID from QR data")
		return c.JSON(http.StatusBadRequest, "invalid device ID in QR code")
	}
	// Now get the door client using the device ID from QR data
	doorClient := h.ClientState.GetDoorClientsByDoorId(doorId64)
	if doorClient == nil {
		h.logger.Error("Door client not found",
			wspkg.Error(err),
			wspkg.String("device_id", qrData.ReqDeviceId),
			wspkg.String("endpoint", "OpenDoorWSPkg"))
		return c.JSON(http.StatusBadRequest, "target device not connected")
	}

	// Check if door is processing
	if doorClient.CheckIsProcessing() {
		h.logger.Error("Door client is already processing",
			wspkg.String("device_id", qrData.ReqDeviceId),
			wspkg.String("endpoint", "OpenDoorWSPkg"))
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINUSE, "door is already in use"))
	}

	// Validate QR client exists and is connected
	qrClient, err := h.ClientState.GetQRClient(qrData.ClientId)
	if err != nil {
		h.logger.Error("QR client not found",
			wspkg.Error(err),
			wspkg.String("client_id", qrData.ClientId),
			wspkg.String("endpoint", "OpenDoorWSPkg"))
		return c.JSON(http.StatusBadRequest, "QR client not connected")
	}

	// Validate QR type matches
	if qrClient.CheckType() != qrData.ConnType {
		h.logger.Error("QR type mismatch",
			wspkg.String("expected", qrClient.CheckType()),
			wspkg.String("got", qrData.ConnType),
			wspkg.String("client_id", qrData.ClientId),
			wspkg.String("endpoint", "OpenDoorWSPkg"))
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINVALIED, "QR type mismatch"))
	}

	h.logger.Info("QR client validated successfully",
		wspkg.String("client_id", qrData.ClientId),
		wspkg.String("conn_type", qrData.ConnType),
		wspkg.String("endpoint", "OpenDoorWSPkg"))

	// CRITICAL FIX: Set processing state for BOTH QR devices (entry and exit)
	err = h.sendProcessingMessages(doorClient, true)
	if err != nil {
		h.logger.Error("Failed to send processing status to QR devices",
			wspkg.Error(err),
			wspkg.String("device_id", qrData.ReqDeviceId),
			wspkg.String("endpoint", "OpenDoorWSPkg"))
		return c.JSON(http.StatusInternalServerError, "failed to send processing status")
	}

	// Set door processing state
	doorClient.SetIsProcessing(true)

	// Check if user exists, create if not
	var userID int64
	userID, err = h.query.GetAttendeeIDByUserID(c.Request().Context(), int64(req.User.ID))
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// Create new user
			h.logger.Info("Creating new user",
				wspkg.Int("user_id", req.User.ID),
				wspkg.String("username", req.User.Name),
				wspkg.String("endpoint", "OpenDoorWSPkg"))

			err := h.query.InsertDoorLockUser(c.Request().Context(), database.InsertDoorLockUserParams{
				UserID:    int64(req.User.ID),
				Username:  req.User.Name,
				BranchID:  int64(qrData.BranchId),
				Email:     sql.NullString{String: req.User.Email, Valid: true},
				Role:      sql.NullString{String: req.User.Role, Valid: true},
				Phone:     sql.NullString{String: req.User.Phone, Valid: true},
				Nic:       sql.NullString{String: req.User.NIC, Valid: true},
				AvatarUrl: sql.NullString{String: req.User.AvatarURL, Valid: true},
			})
			if err != nil {
				h.logger.Error("Failed to insert user",
					wspkg.Error(err),
					wspkg.Int("user_id", req.User.ID),
					wspkg.String("endpoint", "OpenDoorWSPkg"))
				h.sendErrorMessages(doorClient, "failed to create user")
				return c.JSON(http.StatusInternalServerError, "failed to insert user")
			}

			userID, err = h.query.GetAttendeeIDByUserID(c.Request().Context(), int64(req.User.ID))
			if err != nil {
				h.logger.Error("Failed to get user ID after creation",
					wspkg.Error(err),
					wspkg.Int("user_id", req.User.ID),
					wspkg.String("endpoint", "OpenDoorWSPkg"))
				h.sendErrorMessages(doorClient, "failed to get user id")
				return c.JSON(http.StatusInternalServerError, "failed to get user id")
			}
		} else {
			h.logger.Error("Database error during user lookup",
				wspkg.Error(err),
				wspkg.Int("user_id", req.User.ID),
				wspkg.String("endpoint", "OpenDoorWSPkg"))
			h.sendErrorMessages(doorClient, "user lookup failed")
			return c.JSON(http.StatusBadRequest, "user not found")
		}
	}

	// Check attendance logic
	timeNow := time.Now()
	dateOnly := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0, time.UTC)

	err = h.validateAttendanceLogic(c, req.User, qrData, dateOnly, doorClient)
	if err != nil {
		return err // Error response already sent in validateAttendanceLogic
	}

	// Check if door is already open
	if doorClient.IsOpen() {
		h.logger.Warn("Door is already open",
			wspkg.String("device_id", qrData.ReqDeviceId),
			wspkg.String("endpoint", "OpenDoorWSPkg"))
		h.sendErrorMessages(doorClient, "door already open")
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.DoorOpen, "door open already"))
	}

	// CRITICAL FIX: Use event system for door unlock - attendance records will be created in OnOpenDoor event handler
	err = h.triggerDoorUnlock(qrData.ReqDeviceId, userID, req.User, qrData)
	if err != nil {
		h.logger.Error("Failed to trigger door unlock",
			wspkg.Error(err),
			wspkg.String("device_id", qrData.ReqDeviceId),
			wspkg.String("endpoint", "OpenDoorWSPkg"))
		h.sendErrorMessages(doorClient, "failed to unlock door")
		return c.JSON(http.StatusInternalServerError, "failed to trigger door unlock")
	}

	// CRITICAL FIX: DO NOT create attendance records here - they are created in OnOpenDoor event handler
	// This ensures proper event flow and prevents duplicate records

	// Send success messages to QR clients
	// err = h.sendSuccessMessages(doorClient)
	// if err != nil {
	// 	h.logger.Warn("Failed to send success messages",
	// 		wspkg.Error(err),
	// 		wspkg.String("device_id", qrData.ReqDeviceId),
	// 		wspkg.String("endpoint", "OpenDoorWSPkg"))
	// 	// Don't fail the request for success message failure
	// }

	// CRITICAL FIX: Remove "New" message handling - use proper event system instead
	// The onClose event handler will handle necessary cleanup

	h.logger.Info("Door opened successfully",
		wspkg.Int("user_id", req.User.ID),
		wspkg.String("username", req.User.Name),
		wspkg.String("device_id", qrData.ReqDeviceId),
		wspkg.String("conn_type", qrData.ConnType),
		wspkg.String("endpoint", "OpenDoorWSPkg"))

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    true,
		"message": "Door opened successfully",
		"user":    req.User.Name,
		"device":  qrData.ReqDeviceId,
	})
}

// sendQRProcessingStatus sends processing status to a specific QR client
func (h *WSPkgHttpRoutes) sendQRProcessingStatus(clientID string, processing bool) error {
	status := "true"
	if !processing {
		status = "false"
	}

	message := wspkg.NewMessage("processing", []byte(fmt.Sprintf(`{"isProcessing": "%s"}`, status)))

	client, exists := h.manager.GetClient(clientID)
	if !exists || client == nil || !client.IsOnline() {
		return fmt.Errorf("QR client %s not available", clientID)
	}

	client.Send(message)
	h.logger.Debug("Sent processing status to QR client",
		wspkg.String("status", status),
		wspkg.String("client_id", clientID),
		wspkg.String("function", "sendQRProcessingStatus"))
	return nil
}

// sendProcessingMessages sends processing status messages to QR clients
func (h *WSPkgHttpRoutes) sendProcessingMessages(doorClient *wspkg.DoorClient, processing bool) error {
	status := "true"
	if !processing {
		status = "false"
	}

	message := wspkg.NewMessage("processing", []byte(fmt.Sprintf(`{"isProcessing": "%s"}`, status)))

	var errors []string

	// Send to QR-in client if it exists
	qrIn := doorClient.GetQRIn()
	if qrIn != nil {
		client, exists := h.manager.GetClient(qrIn.Id)
		if exists && client != nil && client.IsOnline() {
			client.Send(message)
			h.logger.Debug("Sent processing message to QR-in client",
				wspkg.String("status", status),
				wspkg.String("client_id", qrIn.Id),
				wspkg.String("function", "sendProcessingMessages"))
		} else {
			errors = append(errors, fmt.Sprintf("QR-in client %s not available", qrIn.Id))
		}
	}

	// Send to QR-out client if it exists
	qrOut := doorClient.GetQROut()
	if qrOut != nil {
		client, exists := h.manager.GetClient(qrOut.Id)
		if exists && client != nil && client.IsOnline() {
			client.Send(message)
			h.logger.Debug("Sent processing message to QR-out client",
				wspkg.String("status", status),
				wspkg.String("client_id", qrOut.Id),
				wspkg.String("function", "sendProcessingMessages"))
		} else {
			errors = append(errors, fmt.Sprintf("QR-out client %s not available", qrOut.Id))
		}
	}

	if len(errors) > 0 {
		h.logger.Warn("Some processing messages failed to send",
			wspkg.Strings("errors", errors),
			wspkg.String("function", "sendProcessingMessages"))
		return fmt.Errorf("failed to send processing messages: %v", errors)
	}

	return nil
}

// triggerDoorUnlock triggers door unlock using the event system
func (h *WSPkgHttpRoutes) triggerDoorUnlock(doorClientID string, userID int64, user types.TmpUser, qrData utils.QRCodeInfo) error {
	// Convert device ID to int64
	deviceIntId, err := strconv.ParseInt(qrData.ReqDeviceId, 10, 64)
	if err != nil {
		return fmt.Errorf("failed to parse device ID: %w", err)
	}

	clientId, err := h.ClientState.GetClientIdbyDoorId(deviceIntId)
	if err != nil {
		return fmt.Errorf("failed to get client ID for device %d: %w", deviceIntId, err)
	}

	// Convert service data
	serviceData, err := types.ServiceDataToNullString(user.ServiceData)
	if err != nil {
		h.logger.Error("Failed to convert service data",
			wspkg.Error(err),
			wspkg.String("function", "triggerDoorUnlock"))
	}

	// Convert gym data
	gymData, err := types.GymDataToNullString(&user.GymData)
	if err != nil {
		h.logger.Error("Failed to convert gym data",
			wspkg.Error(err),
			wspkg.String("function", "triggerDoorUnlock"))
	}

	// Create attendance data for the event
	attendData := database.CreateAttendeeParams{
		AttendeeID: userID,
		State:      qrData.ConnType,
		BranchID:   int64(qrData.BranchId),
		DeviceID: sql.NullInt64{
			Int64: deviceIntId,
			Valid: true,
		},
		Subscription: sql.NullString{
			Valid: false,
		},
		Booktype: sql.NullString{
			String: user.BookType,
			Valid:  true,
		},
		ServiceData: serviceData,
		GymData:     gymData,
	}

	// Create message for the open-door event
	msg, err := wspkg.NewMessageFromJSON("open-door", attendData)
	if err != nil {
		return fmt.Errorf("failed to create open-door message: %w", err)
	}

	// Send the message to the door client to trigger the event
	client, exists := h.manager.GetClient(clientId)
	if !exists || client == nil || !client.IsOnline() {
		return fmt.Errorf("door client %s not available", doorClientID)
	}

	client.Send(msg)
	h.logger.Info("Triggered door unlock event",
		wspkg.String("client_id", doorClientID),
		wspkg.Any("user_id", userID),
		wspkg.String("conn_type", qrData.ConnType),
		wspkg.String("function", "triggerDoorUnlock"))
	return nil
}

// sendSuccessMessages sends success messages to QR clients
func (h *WSPkgHttpRoutes) sendSuccessMessages(doorClient *wspkg.DoorClient) error {
	successMessage := wspkg.NewMessage("unlock-success", []byte(`{"status": "success", "message": "Door opened successfully"}`))

	var errors []string

	// Send to QR-in client if it exists
	qrIn := doorClient.GetQRIn()
	if qrIn != nil {
		client, exists := h.manager.GetClient(qrIn.Id)
		if exists && client != nil && client.IsOnline() {
			client.Send(successMessage)
			h.logger.Debug("Sent success message to QR-in client",
				wspkg.String("client_id", qrIn.Id),
				wspkg.String("function", "sendSuccessMessages"))
		} else {
			errors = append(errors, fmt.Sprintf("QR-in client %s not available", qrIn.Id))
		}
	}

	// Send to QR-out client if it exists
	qrOut := doorClient.GetQROut()
	if qrOut != nil {
		client, exists := h.manager.GetClient(qrOut.Id)
		if exists && client != nil && client.IsOnline() {
			client.Send(successMessage)
			h.logger.Debug("Sent success message to QR-out client",
				wspkg.String("client_id", qrOut.Id),
				wspkg.String("function", "sendSuccessMessages"))
		} else {
			errors = append(errors, fmt.Sprintf("QR-out client %s not available", qrOut.Id))
		}
	}

	if len(errors) > 0 {
		h.logger.Warn("Some success messages failed to send",
			wspkg.Strings("errors", errors),
			wspkg.String("function", "sendSuccessMessages"))
		// Don't return error for success messages as they're not critical
	}

	return nil
}

// CRITICAL FIX: Remove sendGenerateNewMessage - use proper event system instead
// The onClose event handler will handle necessary cleanup

// sendErrorMessages sends error messages to QR clients
func (h *WSPkgHttpRoutes) sendErrorMessages(doorClient *wspkg.DoorClient, errorMsg string) {
	errorMessage := wspkg.NewMessage("processError", []byte(fmt.Sprintf(`{"isProcessing": false, "error": "%s"}`, errorMsg)))

	// Send to QR-in client if it exists
	qrIn := doorClient.GetQRIn()
	if qrIn != nil {
		client, exists := h.manager.GetClient(qrIn.Id)
		if exists && client != nil && client.IsOnline() {
			client.Send(errorMessage)
			h.logger.Debug("Sent error message to QR-in client",
				wspkg.String("client_id", qrIn.Id),
				wspkg.String("error", errorMsg),
				wspkg.String("function", "sendErrorMessages"))
		}
	}

	// Send to QR-out client if it exists
	qrOut := doorClient.GetQROut()
	if qrOut != nil {
		client, exists := h.manager.GetClient(qrOut.Id)
		if exists && client != nil && client.IsOnline() {
			client.Send(errorMessage)
			h.logger.Debug("Sent error message to QR-out client",
				wspkg.String("client_id", qrOut.Id),
				wspkg.String("error", errorMsg),
				wspkg.String("function", "sendErrorMessages"))
		}
	}
}

// validateAttendanceLogic validates the attendance logic for the user
func (h *WSPkgHttpRoutes) validateAttendanceLogic(ctx echo.Context, user types.TmpUser, qrData utils.QRCodeInfo, dateOnly time.Time, doorClient *wspkg.DoorClient) error {
	if qrData.ConnType == "qr-in" {
		// For entry, check if user has already entered today
		isAlreadyIn, err := h.query.CheckAttendanceRecordExists(ctx.Request().Context(), database.CheckAttendanceRecordExistsParams{
			UserID: int64(user.ID),
			State:  "qr-in",
			Time: sql.NullTime{
				Time:  dateOnly,
				Valid: true,
			},
			Booktype: sql.NullString{
				String: user.BookType,
				Valid:  true,
			},
		})
		if err != nil {
			h.logger.Error("Database error checking entry attendance",
				wspkg.Error(err),
				wspkg.Int("user_id", user.ID),
				wspkg.String("function", "validateAttendanceLogic"))
			h.sendErrorMessages(doorClient, "attendance check failed")
			return ctx.JSON(http.StatusInternalServerError, "attendance check failed")
		}

		if isAlreadyIn {
			// Check if user has already exited (can re-enter if exited)
			h.logger.Warn("User already entered today",
				wspkg.Int("user_id", user.ID),
				wspkg.String("function", "validateAttendanceLogic"))
			h.sendErrorMessages(doorClient, "user already in")
			return ctx.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.USERALREADYIN, "user already in"))
		}
	} else if qrData.ConnType == "qr-out" {
		// For exit, check if user has entered today
		hasCheckedIn, err := h.query.CheckAttendanceRecordExists(ctx.Request().Context(), database.CheckAttendanceRecordExistsParams{
			UserID: int64(user.ID),
			State:  "qr-in",
			Time: sql.NullTime{
				Time:  dateOnly,
				Valid: true,
			},
			Booktype: sql.NullString{
				String: user.BookType,
				Valid:  true,
			},
		})
		if err != nil {
			h.logger.Error("Database error checking entry for exit validation",
				wspkg.Error(err),
				wspkg.Int("user_id", user.ID),
				wspkg.String("function", "validateAttendanceLogic"))
			h.sendErrorMessages(doorClient, "attendance check failed")
			return ctx.JSON(http.StatusInternalServerError, "attendance check failed")
		}

		if !hasCheckedIn {
			h.logger.Warn("User has not checked in today",
				wspkg.Int("user_id", user.ID),
				wspkg.String("function", "validateAttendanceLogic"))
			h.sendErrorMessages(doorClient, "user must check in before checking out")
			return ctx.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.USERNOTCHECKEDIN, "user must check in before checking out"))
		}

		// Check if user has already exited
		hasCheckedOut, err := h.query.CheckAttendanceRecordExists(ctx.Request().Context(), database.CheckAttendanceRecordExistsParams{
			UserID: int64(user.ID),
			State:  "qr-out",
			Time: sql.NullTime{
				Time:  dateOnly,
				Valid: true,
			},
			Booktype: sql.NullString{
				String: user.BookType,
				Valid:  true,
			},
		})
		if err != nil {
			h.logger.Error("Database error checking exit attendance",
				wspkg.Error(err),
				wspkg.Int("user_id", user.ID),
				wspkg.String("function", "validateAttendanceLogic"))
			h.sendErrorMessages(doorClient, "attendance check failed")
			return ctx.JSON(http.StatusInternalServerError, "attendance check failed")
		}

		if hasCheckedOut {
			h.logger.Warn("User already checked out today",
				wspkg.Int("user_id", user.ID),
				wspkg.String("function", "validateAttendanceLogic"))
			h.sendErrorMessages(doorClient, "user already checked out")
			return ctx.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.USERALREADYOUT, "user already checked out"))
		}
	}

	return nil
}

// CRITICAL FIX: Remove createAttendanceRecord function
// Attendance records should ONLY be created in the OnOpenDoor event handler
// This ensures proper event flow and prevents duplicate records
